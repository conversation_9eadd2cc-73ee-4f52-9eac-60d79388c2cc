[15:27:47] 已从 db_config.json 加载配置。
[15:27:49] 正在测试 source 数据库连接...
[15:27:49] 正在测试连接到 *************...
[15:27:49] 连接成功！
[15:27:49] 正在测试 dest 数据库连接...
[15:27:49] 正在测试连接到 *************...
[15:27:49] 连接成功！
[15:27:52] 
==============================================
[15:27:52]           开始执行迁移任务...           
[15:27:52] ==============================================
[15:27:52] 步骤 1/2: 迁移数据和结构
[15:27:52] --- 开始迁移数据和结构 ---
[15:27:52] 连接源数据库...
[15:27:52] ✗ 源数据库连接失败: Character set 'utf8' unsupported
[15:27:52] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[15:27:52] 所有数据库连接已关闭。
[15:27:52] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[15:39:36] 已从 db_config.json 加载配置。
[15:41:37] 已从 db_config.json 加载配置。
[15:41:43] 正在测试 source 数据库连接...
[15:41:43] 正在测试连接到 *************...
[15:41:43] 连接成功！
[15:41:44] 正在测试 dest 数据库连接...
[15:41:44] 正在测试连接到 *************...
[15:41:44] 连接成功！
[15:41:46] 
==============================================
[15:41:46]           开始执行迁移任务...           
[15:41:46] ==============================================
[15:41:46] 步骤 1/2: 迁移数据和结构
[15:41:46] --- 开始迁移数据和结构 ---
[15:41:46] 连接源数据库...
[15:41:46] ✗ 源数据库连接失败: Character set 'utf8' unsupported
[15:41:46] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[15:41:46] 所有数据库连接已关闭。
[15:41:46] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[15:55:53] 已从 db_config.json 加载配置。
[15:58:08] 正在测试 source 数据库连接...
[15:58:08] 正在测试连接到 *************...
[15:58:08] 调试: 连接参数 - host=*************, port=3306, user=root
[15:58:08] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[15:58:08] 连接成功！
[15:58:09] 正在测试 dest 数据库连接...
[15:58:09] 正在测试连接到 *************...
[15:58:09] 调试: 连接参数 - host=*************, port=3306, user=root
[15:58:09] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[15:58:09] 连接成功！
[15:58:11] 
==============================================
[15:58:11]           开始执行迁移任务...           
[15:58:11] ==============================================
[15:58:11] 调试: 迁移任务类型 - full
[15:58:11] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[15:58:11] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[15:58:11] 步骤 1/2: 迁移数据和结构
[15:58:11] --- 开始迁移数据和结构 ---
[15:58:11] 连接源数据库...
[15:58:11] 调试: 连接参数 - host=*************, port=3306, user=root
[15:58:11] ✗ 数据库连接失败: Character set 'utf8' unsupported
[15:58:11] 调试: 错误详情 - errno: -1, sqlstate: None
[15:58:11] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[15:58:11] 所有数据库连接已关闭。
[15:58:11] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[16:00:55] 已从 db_config.json 加载配置。
[16:00:57] 正在测试 source 数据库连接...
[16:00:57] 正在测试连接到 *************...
[16:00:57] 调试: 连接参数 - host=*************, port=3306, user=root
[16:00:57] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[16:00:57] 连接成功！
[16:00:57] 正在测试 dest 数据库连接...
[16:00:57] 正在测试连接到 *************...
[16:00:57] 调试: 连接参数 - host=*************, port=3306, user=root
[16:00:57] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[16:00:57] 连接成功！
[16:00:58] 
==============================================
[16:00:58]           开始执行迁移任务...           
[16:00:58] ==============================================
[16:00:58] 调试: 迁移任务类型 - full
[16:00:58] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[16:00:58] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[16:00:58] 步骤 1/2: 迁移数据和结构
[16:00:58] --- 开始迁移数据和结构 ---
[16:00:58] 连接源数据库...
[16:00:58] 调试: 连接参数 - host=*************, port=3306, user=root
[16:00:58] 调试: 基本连接失败，尝试其他方法 - Character set 'utf8' unsupported
[16:00:58] 调试: utf8mb4连接失败，尝试utf8 - Character set 'utf8' unsupported
[16:00:58] 调试: utf8连接也失败 - Character set 'utf8' unsupported
[16:00:58] ✗ 数据库连接失败: Character set 'utf8' unsupported
[16:00:58] 调试: 错误详情 - errno: -1, sqlstate: None
[16:00:58] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[16:00:58] 所有数据库连接已关闭。
[16:00:58] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
[16:06:51] 已从 db_config.json 加载配置。
[17:08:53] 已从 db_config.json 加载配置。
[17:09:04] 已从 db_config.json 加载配置。
[17:09:05] 已从 db_config.json 加载配置。
[17:09:09] 已从 db_config.json 加载配置。
[17:09:10] 正在测试 source 数据库连接...
[17:09:10] 正在测试连接到 *************...
[17:09:10] 调试: 连接参数 - host=*************, port=3306, user=root
[17:09:10] ✓ 数据库连接成功 - 字符集: utf8mb3, 排序规则: utf8mb3_general_ci
[17:09:10] 连接成功！
[17:09:11] 正在测试 dest 数据库连接...
[17:09:11] 正在测试连接到 *************...
[17:09:11] 调试: 连接参数 - host=*************, port=3306, user=root
[17:09:11] ✓ 数据库连接成功 - 字符集: utf8mb4, 排序规则: utf8mb4_unicode_ci
[17:09:11] 连接成功！
[17:09:13] 
==============================================
[17:09:13]           开始执行迁移任务...           
[17:09:13] ==============================================
[17:09:13] 调试: 迁移任务类型 - full
[17:09:13] 调试: 源数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:09:13] 调试: 目标数据库配置 - host=*************, port=3306, user=root, charset=utf8mb4
[17:09:13] 步骤 1/2: 迁移数据和结构
[17:09:13] --- 开始迁移数据和结构 ---
[17:09:13] 连接源数据库...
[17:09:13] 调试: 连接参数 - host=*************, port=3306, user=root
[17:09:13] ✗ 数据库连接失败: Character set 'utf8' unsupported
[17:09:13] 调试: 错误详情 - errno: -1, sqlstate: None
[17:09:13] 
[错误] 数据库操作失败: Character set 'utf8' unsupported
[17:09:13] 所有数据库连接已关闭。
[17:09:13] 
❌❌❌ 迁移任务中途失败，请检查以上日志。 ❌❌❌
