-- #####################################################################
-- # 用户和权限迁移脚本
-- # 生成来源: Python 脚本
-- #
-- # 重要提示:
-- # 1. 在目标 MySQL 服务器上执行此脚本前，请务必仔细审查！
-- # 2. 所有用户均使用临时密码 'please_change_password_after_migration' 创建。
-- #    迁移后必须立即为每个用户设置新密码。
-- # 3. 请检查每个用户的的主机名 (如 '@'localhost', '@'192.168.1.%%') 
-- #    是否适用于您的新网络环境，并按需修改。
-- #####################################################################


-- ---- 用户: gi@% ----
CREATE USER IF NOT EXISTS 'gi'@'%' IDENTIFIED WITH mysql_native_password BY 'please_change_password_after_migration';
GRANT ALL PRIVILEGES ON `gi888`.* TO `gi`@`%`;

-- ---- 用户: taobao@% ----
CREATE USER IF NOT EXISTS 'taobao'@'%' IDENTIFIED WITH mysql_native_password BY 'please_change_password_after_migration';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, REFERENCES, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, EVENT, TRIGGER ON `taobao_db`.* TO `taobao`@`%`;

-- 应用所有权限变更
FLUSH PRIVILEGES;
